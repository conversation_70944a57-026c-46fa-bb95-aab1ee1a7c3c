# API 文档

Data Agent 提供基于FastAPI的RESTful API接口，支持智能查询和数据处理功能。

## 基础信息

- **基础URL**: `http://localhost:8000`
- **API版本**: v1.0
- **认证方式**: 暂无（开发阶段）

## 智能体API

### 调用智能体

**端点**: `POST /data-agent/invoke`

**描述**: 向智能体发送单个查询请求

**请求体**:
```json
{
  "input": {
    "messages": [
      {
        "role": "user",
        "content": "技术部有多少人？"
      }
    ]
  }
}
```

**响应**:
```json
{
  "output": {
    "messages": [
      {
        "role": "assistant",
        "content": "根据数据库查询结果，技术部目前有15名员工。"
      }
    ]
  }
}
```

### 流式调用智能体

**端点**: `POST /data-agent/stream`

**描述**: 向智能体发送查询请求，以流式方式返回响应

**请求体**:
```json
{
  "input": {
    "messages": [
      {
        "role": "user", 
        "content": "查询销售部门的平均薪资"
      }
    ]
  }
}
```

**响应**: Server-Sent Events (SSE) 流

### 批量调用智能体

**端点**: `POST /data-agent/batch`

**描述**: 批量处理多个查询请求

**请求体**:
```json
{
  "inputs": [
    {
      "messages": [{"role": "user", "content": "查询1"}]
    },
    {
      "messages": [{"role": "user", "content": "查询2"}]
    }
  ]
}
```

## 健康检查

### 服务状态

**端点**: `GET /`

**描述**: 检查服务是否正常运行

**响应**: 重定向到 `/docs`

### API文档

**端点**: `GET /docs`

**描述**: Swagger UI API文档界面

**端点**: `GET /redoc`

**描述**: ReDoc API文档界面

## 错误处理

### 错误响应格式

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息"
  }
}
```

### 常见错误码

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| `INVALID_INPUT` | 400 | 请求参数无效 |
| `DATABASE_ERROR` | 500 | 数据库连接错误 |
| `API_KEY_ERROR` | 401 | API密钥无效 |
| `RATE_LIMIT` | 429 | 请求频率超限 |

## 使用示例

### Python客户端

```python
import requests

# 基础查询
response = requests.post(
    "http://localhost:8000/data-agent/invoke",
    json={
        "input": {
            "messages": [
                {"role": "user", "content": "技术部有多少人？"}
            ]
        }
    }
)

result = response.json()
print(result["output"]["messages"][-1]["content"])
```

### JavaScript客户端

```javascript
// 基础查询
fetch('http://localhost:8000/data-agent/invoke', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    input: {
      messages: [
        {role: 'user', content: '技术部有多少人？'}
      ]
    }
  })
})
.then(response => response.json())
.then(data => {
  console.log(data.output.messages[data.output.messages.length - 1].content);
});
```

### 流式查询示例

```python
import requests

response = requests.post(
    "http://localhost:8000/data-agent/stream",
    json={
        "input": {
            "messages": [
                {"role": "user", "content": "分析销售数据趋势"}
            ]
        }
    },
    stream=True
)

for line in response.iter_lines():
    if line:
        print(line.decode('utf-8'))
```

## 智能体能力

### 支持的查询类型

1. **数据库查询**
   - 员工信息查询
   - 部门统计
   - 薪资分析
   - 自定义SQL查询

2. **Web搜索**
   - 通用信息查询
   - 实时数据获取
   - 新闻和事件查询

3. **数据分析**
   - 统计计算
   - 趋势分析
   - 数据可视化建议

### 查询示例

```
用户: "技术部有多少人？他们的平均薪水是多少？"
智能体: "根据数据库查询，技术部目前有15名员工，平均薪资为12,500元。"

用户: "最近有什么重要的科技新闻？"
智能体: "根据最新搜索结果，以下是近期重要科技新闻：..."

用户: "帮我分析一下各部门的人员分布"
智能体: "我来为您查询并分析各部门的人员分布情况..."
```

## 限制和注意事项

1. **请求频率**: 建议每秒不超过10个请求
2. **响应时间**: 复杂查询可能需要10-30秒
3. **数据安全**: 请勿在查询中包含敏感信息
4. **API稳定性**: 当前为开发版本，API可能会有变更

## 更新日志

### v1.0.0 (当前版本)
- 初始API版本
- 支持基础智能体调用
- 集成数据库查询和Web搜索
- 提供流式响应支持

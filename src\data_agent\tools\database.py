"""
数据库工具模块
提供SQL数据库查询和操作功能
"""
import os
from dotenv import load_dotenv

from langchain_community.utilities.sql_database import SQLDatabase
from langchain_community.agent_toolkits import SQLDatabaseToolkit
from langchain_deepseek import ChatDeepSeek

# 加载 .env 文件中的环境变量
load_dotenv()


def create_sql_tools():
    """创建SQL数据库工具集"""
    # 1. 初始化数据库连接
    # 我们使用 f-string 来构建数据库URI，并从环境变量中读取凭证
    db_uri = f"mysql+mysqlconnector://{os.getenv('DB_USER')}:{os.getenv('DB_PASSWORD')}@{os.getenv('DB_HOST')}:{os.getenv('DB_PORT')}/{os.getenv('DB_NAME')}"
    db = SQLDatabase.from_uri(db_uri)
    
    # 2. 初始化大模型 (需要一个模型来将自然语言转为SQL)
    llm = ChatDeepSeek(
        model="deepseek-chat", 
        api_key=os.getenv("DEEPSEEK_API_KEY"),
        temperature=0  # SQL生成需要精确，所以温度设为0
    )
    
    # 3. 创建SQL工具包
    # 这个工具包包含了"查询SQL"、"列出表"、"检查表结构"等一系列子工具
    sql_toolkit = SQLDatabaseToolkit(db=db, llm=llm)
    
    # 4. 获取所有SQL相关的工具列表
    sql_tools = sql_toolkit.get_tools()
    
    return sql_tools, db


if __name__ == '__main__':
    tools, database = create_sql_tools()
    print("----- SQL Tools Initialized -----")
    print(f"Database URI: {database.get_dialect()} connection to {os.getenv('DB_NAME')}")
    print(f"Sample Table Info: {database.get_table_info()}")
    print("\nAvailable SQL Tools:")
    for tool in tools:
        print(f"- {tool.name}: {tool.description}")

"""
Data Agent 主入口文件

提供多种启动方式：
- 启动Web服务器
- 运行数据处理脚本
- 交互式命令行
"""

import argparse
import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))


def start_server():
    """启动Web服务器"""
    from data_agent.core.server import start_server
    print("🚀 启动Data Agent Web服务器...")
    start_server()


def run_personnel_script():
    """运行外部人员数据处理脚本"""
    import subprocess
    script_path = Path(__file__).parent / "scripts" / "generate_external_personnel.py"
    print("📊 运行外部人员数据处理脚本...")
    subprocess.run([sys.executable, str(script_path)])


def interactive_mode():
    """交互式模式"""
    from data_agent.agents.graph import agent_executor

    print("🤖 Data Agent 交互式模式")
    print("输入 'quit' 或 'exit' 退出")
    print("-" * 50)

    while True:
        try:
            user_input = input("\n用户: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("再见！")
                break

            if not user_input:
                continue

            print("Agent: ", end="", flush=True)
            response = agent_executor.invoke({
                "messages": [{"role": "user", "content": user_input}]
            })
            print(response["messages"][-1].content)

        except KeyboardInterrupt:
            print("\n\n再见！")
            break
        except Exception as e:
            print(f"错误: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Data Agent - 数据处理智能体')
    parser.add_argument(
        'mode',
        choices=['server', 'personnel', 'interactive'],
        nargs='?',
        default='interactive',
        help='运行模式 (默认: interactive)'
    )

    args = parser.parse_args()

    if args.mode == 'server':
        start_server()
    elif args.mode == 'personnel':
        run_personnel_script()
    elif args.mode == 'interactive':
        interactive_mode()


if __name__ == "__main__":
    main()

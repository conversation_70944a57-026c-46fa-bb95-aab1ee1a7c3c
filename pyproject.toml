[project]
name = "data-agent"
version = "0.1.0"
description = "Data processing agent with external personnel management"
readme = "README.md"
requires-python = ">=3.11"
authors = [
    {name = "Data Agent Team", email = "<EMAIL>"}
]
keywords = ["ai", "agent", "data-processing", "langchain", "excel"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    # 核心框架
    "langchain",
    "langgraph",
    "langchain-community",
    "langchain-core",
    "langchain-deepseek",
    "langchain_experimental",  # For SQL agent tools

    # 工具
    "tavily-python",
    "mysql-connector-python",

    # 后端服务
    "fastapi",
    "uvicorn",
    "sse-starlette",

    # 读取 .env 文件
    "python-dotenv",

    # <PERSON><PERSON><PERSON> 测试与调试
    "langsmith",

    # Excel 和数据处理
    "et_xmlfile==2.0.0",
    "numpy==1.26.4",
    "openpyxl==3.1.2",
    "pandas==2.1.4",
    "psutil==5.9.8",
    "python-dateutil==2.9.0.post0",
    "pytz==2025.2",
    "pywin32==306",
    "six==1.17.0",
    "tzdata==2025.2",
    "xlrd==2.0.1",
    "xlwt==1.3.0",
]

[project.scripts]
data-agent = "main:main"
generate-personnel = "scripts.generate_external_personnel:main"

[project.urls]
Homepage = "https://github.com/your-org/data-agent"
Repository = "https://github.com/your-org/data-agent"
Documentation = "https://github.com/your-org/data-agent/docs"
Issues = "https://github.com/your-org/data-agent/issues"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/data_agent"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/scripts",
    "/data",
    "/config",
    "/docs",
    "/tests",
]

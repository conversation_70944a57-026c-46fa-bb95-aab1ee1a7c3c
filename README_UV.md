# UV 包管理迁移说明

## 概述

本项目已成功从传统的 `requirements.txt` 迁移到使用 `uv` 包管理器。所有依赖包已合并到 `pyproject.toml` 文件中。

## 迁移内容

### 合并的依赖包

**来自 requirements.txt:**
- langchain 相关包（langchain, langgraph, langchain-community, langchain-core, langchain-deepseek, langchain_experimental）
- 工具包（tavily-python, mysql-connector-python）
- 后端服务（fastapi, uvicorn, sse-starlette）
- 配置文件（python-dotenv）
- 调试工具（langsmith）

**来自 requirements22.txt (generate_external_personnel.py 所需):**
- Excel 处理（et_xmlfile, openpyxl, xlrd, xlwt）
- 数据处理（numpy, pandas）
- 系统工具（psutil）
- 时间处理（python-dateutil, pytz, tzdata）
- Windows 支持（pywin32）
- 工具包（six）

## 使用 UV 包管理器

### 安装依赖
```bash
uv sync
```

### 运行 Python 脚本
```bash
uv run python app/generate_external_personnel.py
```

### 添加新依赖
```bash
uv add package_name
```

### 移除依赖
```bash
uv remove package_name
```

### 更新依赖
```bash
uv sync --upgrade
```

## 文件说明

- `pyproject.toml`: 项目配置和依赖定义
- `uv.lock`: 锁定的依赖版本（类似于 package-lock.json）
- 已删除: `requirements.txt`, `requirements22.txt`

## 验证

所有依赖包已成功安装并验证，`generate_external_personnel.py` 可以正常运行。

## 优势

1. **更快的依赖解析**: uv 比 pip 快 10-100 倍
2. **更好的依赖管理**: 自动解决版本冲突
3. **锁定文件**: 确保环境一致性
4. **现代化工具**: 支持 PEP 621 标准

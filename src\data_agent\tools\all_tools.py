"""
工具集合模块
整合所有可用的工具
"""
from .database import create_sql_tools
from .search import create_search_tool


def get_all_tools():
    """获取所有可用的工具"""
    # 获取SQL工具
    sql_tools, _ = create_sql_tools()
    
    # 获取搜索工具
    search_tool = create_search_tool()
    
    # 组合所有工具
    all_tools = sql_tools + [search_tool]
    
    return all_tools


if __name__ == '__main__':
    tools = get_all_tools()
    print("----- All Tools Initialized -----")
    print("\nAvailable Tools:")
    for tool in tools:
        print(f"- {tool.name}: {tool.description}")

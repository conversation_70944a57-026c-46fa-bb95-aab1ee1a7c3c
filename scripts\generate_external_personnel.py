#!/usr/bin/env python3
"""
外部人员管理数据生成脚本

这个脚本用于处理外部人员管理的Excel数据，包括：
- 数据清洗和转换
- ID映射和验证
- 输出格式转换

使用方法:
    python scripts/generate_external_personnel.py [--input INPUT_FILE] [--output OUTPUT_DIR]
"""

import argparse
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data_agent.core.personnel_processor import PersonnelDataProcessor
from src.data_agent.utils.excel_utils import safe_excel_write, convert_to_xls


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成外部人员管理数据')
    parser.add_argument(
        '--input', 
        default='data/raw/底表.xlsx',
        help='输入的底表Excel文件路径 (默认: data/raw/底表.xlsx)'
    )
    parser.add_argument(
        '--output-dir',
        default='data/processed',
        help='输出目录 (默认: data/processed)'
    )
    parser.add_argument(
        '--format',
        choices=['xlsx', 'xls', 'both'],
        default='both',
        help='输出格式 (默认: both)'
    )
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    input_file = Path(args.input)
    if not input_file.exists():
        print(f"错误：输入文件不存在: {input_file}")
        return 1
    
    # 确保输出目录存在
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # 创建处理器并处理数据
        processor = PersonnelDataProcessor(str(input_file))
        result_df = processor.process_all()
        
        # 保存结果
        output_base = output_dir / "外部人员管理"
        
        if args.format in ['xlsx', 'both']:
            xlsx_file = f"{output_base}.xlsx"
            if safe_excel_write(result_df, xlsx_file, '外部人员管理'):
                print(f"✓ Excel文件已保存: {xlsx_file}")
            else:
                print(f"✗ Excel文件保存失败: {xlsx_file}")
                return 1
        
        if args.format in ['xls', 'both']:
            xlsx_file = f"{output_base}.xlsx"
            xls_file = f"{output_base}.xls"
            
            # 如果需要xls格式但没有xlsx文件，先创建xlsx
            if args.format == 'xls' and not Path(xlsx_file).exists():
                if not safe_excel_write(result_df, xlsx_file, '外部人员管理'):
                    print(f"✗ 临时Excel文件创建失败: {xlsx_file}")
                    return 1
            
            # 转换为xls格式
            if convert_to_xls(xlsx_file, xls_file, overwrite=True):
                print(f"✓ XLS文件已保存: {xls_file}")
            else:
                print(f"✗ XLS文件转换失败: {xls_file}")
                return 1
        
        print("\n🎉 外部人员管理数据生成完成！")
        return 0
        
    except Exception as e:
        print(f"✗ 处理过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)

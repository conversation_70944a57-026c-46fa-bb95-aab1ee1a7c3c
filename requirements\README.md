# Requirements 目录说明

这个目录包含不同环境的依赖文件，与 `pyproject.toml` 配合使用。

## 文件说明

### base.txt
基础依赖，引用 `pyproject.toml` 中定义的项目依赖：
```bash
pip install -r requirements/base.txt
```

### dev.txt  
开发环境依赖，包含测试、代码质量工具等：
```bash
pip install -r requirements/dev.txt
```

### prod.txt
生产环境依赖，包含生产服务器相关工具：
```bash
pip install -r requirements/prod.txt
```

## 使用方式

### 方式1：使用 pyproject.toml（推荐）
```bash
# 安装基础依赖
pip install -e .

# 安装开发依赖（如果在 pyproject.toml 中定义了 [project.optional-dependencies]）
pip install -e ".[dev]"
```

### 方式2：使用 requirements 文件
```bash
# 开发环境
pip install -r requirements/dev.txt

# 生产环境  
pip install -r requirements/prod.txt
```

### 方式3：使用 uv（最快）
```bash
# 安装所有依赖
uv sync

# 只安装生产依赖
uv sync --no-dev
```

## 依赖管理策略

1. **主要依赖** 在 `pyproject.toml` 中定义
2. **环境特定依赖** 在对应的 requirements 文件中定义
3. **版本锁定** 可以在 requirements 文件中覆盖
4. **CI/CD** 可以使用 requirements 文件进行部署

## 生成锁定版本文件

使用 pip-tools 生成精确版本的依赖文件：
```bash
# 安装 pip-tools
pip install pip-tools

# 生成锁定版本文件
pip-compile requirements/base.in --output-file requirements/base.lock
pip-compile requirements/dev.in --output-file requirements/dev.lock
```

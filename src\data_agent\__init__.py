"""
Data Agent - 数据处理智能体

这是一个集成了Lang<PERSON>hain和数据处理功能的智能体项目。
主要功能包括：
- 外部人员管理数据处理
- SQL数据库查询智能体
- Web搜索功能
"""

__version__ = "0.1.0"
__author__ = "Data Agent Team"

# 延迟导入，避免在模块加载时就连接数据库
def get_agent_executor():
    """获取智能体执行器"""
    from .agents.graph import agent_executor
    return agent_executor

def get_app():
    """获取FastAPI应用"""
    from .core.server import app
    return app

__all__ = ["get_agent_executor", "get_app"]

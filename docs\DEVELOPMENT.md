# 开发指南

本文档为Data Agent项目的开发者提供详细的开发指南。

## 项目架构

### 核心组件

1. **智能体模块 (agents/)**
   - `graph.py`: LangGraph智能体定义和配置

2. **工具模块 (tools/)**
   - `database.py`: SQL数据库查询工具
   - `search.py`: Web搜索工具
   - `all_tools.py`: 工具集合管理

3. **核心业务 (core/)**
   - `personnel_processor.py`: 外部人员数据处理核心逻辑
   - `server.py`: FastAPI Web服务器

4. **工具函数 (utils/)**
   - `excel_utils.py`: Excel文件处理工具

### 设计原则

- **模块化**: 每个功能模块独立，便于维护和测试
- **可扩展**: 新的工具和功能可以轻松添加
- **配置驱动**: 通过环境变量和配置文件管理设置
- **错误处理**: 完善的异常处理和日志记录

## 开发环境设置

### 1. 克隆仓库
```bash
git clone https://github.com/your-org/data-agent.git
cd data-agent
```

### 2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

### 3. 安装依赖
```bash
pip install -r requirements/dev.txt
```

### 4. 配置环境变量
```bash
cp config/.env.example .env
# 编辑 .env 文件，填入必要的配置
```

## 代码规范

### Python代码风格
- 使用 [Black](https://black.readthedocs.io/) 进行代码格式化
- 使用 [isort](https://pycqa.github.io/isort/) 进行导入排序
- 遵循 [PEP 8](https://www.python.org/dev/peps/pep-0008/) 规范

### 文档字符串
使用Google风格的文档字符串：
```python
def process_data(input_file: str, output_dir: str) -> bool:
    """处理数据文件
    
    Args:
        input_file: 输入文件路径
        output_dir: 输出目录路径
        
    Returns:
        处理是否成功
        
    Raises:
        FileNotFoundError: 当输入文件不存在时
    """
    pass
```

### 类型注解
所有公共函数和方法都应该包含类型注解：
```python
from typing import List, Dict, Optional

def get_employee_mapping(data: pd.DataFrame) -> Dict[str, int]:
    """获取员工映射字典"""
    pass
```

## 测试

### 运行测试
```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_personnel_processor.py

# 运行测试并生成覆盖率报告
pytest --cov=src/data_agent --cov-report=html
```

### 测试结构
```
tests/
├── unit/                   # 单元测试
│   ├── test_excel_utils.py
│   └── test_personnel_processor.py
├── integration/            # 集成测试
│   └── test_api.py
└── fixtures/               # 测试数据
    └── sample_data.xlsx
```

### 编写测试
```python
import pytest
from src.data_agent.utils.excel_utils import safe_excel_write

def test_safe_excel_write():
    """测试Excel安全写入功能"""
    # 测试代码
    pass
```

## 添加新功能

### 1. 添加新工具
在 `src/data_agent/tools/` 目录下创建新的工具模块：
```python
# src/data_agent/tools/new_tool.py
def create_new_tool():
    """创建新工具"""
    pass
```

在 `all_tools.py` 中注册新工具：
```python
from .new_tool import create_new_tool

def get_all_tools():
    # 现有工具...
    new_tool = create_new_tool()
    all_tools.append(new_tool)
    return all_tools
```

### 2. 添加新的数据处理器
在 `src/data_agent/core/` 目录下创建新的处理器：
```python
# src/data_agent/core/new_processor.py
class NewDataProcessor:
    """新数据处理器"""
    
    def process(self, data):
        """处理数据"""
        pass
```

### 3. 添加新的API端点
在 `src/data_agent/core/server.py` 中添加新路由：
```python
@app.post("/api/new-endpoint")
async def new_endpoint(request: NewRequest):
    """新API端点"""
    pass
```

## 部署

### 开发环境
```bash
python main.py server
```

### 生产环境
```bash
# 使用gunicorn
gunicorn src.data_agent.core.server:app -w 4 -k uvicorn.workers.UvicornWorker

# 使用Docker
docker build -t data-agent .
docker run -p 8000:8000 data-agent
```

## 调试

### 日志配置
在 `.env` 文件中设置日志级别：
```env
LOG_LEVEL=DEBUG
LOG_FILE=logs/data_agent.log
```

### 调试智能体
```python
# 启用LangSmith追踪
import os
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_API_KEY"] = "your_langsmith_key"
```

## 常见问题

### Q: 如何添加新的数据库连接？
A: 在 `tools/database.py` 中修改数据库URI配置。

### Q: 如何自定义智能体提示词？
A: 在 `agents/graph.py` 中修改 `system_prompt` 变量。

### Q: 如何处理Excel文件权限错误？
A: 使用 `utils/excel_utils.py` 中的 `kill_excel_processes()` 函数。

## 贡献流程

1. 创建功能分支
2. 编写代码和测试
3. 运行代码质量检查
4. 提交Pull Request
5. 代码审查
6. 合并到主分支

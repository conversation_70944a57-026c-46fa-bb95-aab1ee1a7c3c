"""
外部人员数据处理核心模块
"""
import pandas as pd
from datetime import datetime
import os


class PersonnelDataProcessor:
    """外部人员数据处理器"""
    
    def __init__(self, base_file_path):
        """
        初始化处理器
        
        Args:
            base_file_path: 底表.xlsx文件路径
        """
        self.base_file_path = base_file_path
        self.template_df = None
        self.team_mapping = None
        self.area_mapping = None
        self.area_name_to_id = None
        self.sales_mapping = None
        self.creator_mapping = None
        self.unit_mapping = None
        
    def load_data(self):
        """加载所有基础数据"""
        # 读取底表.xlsx中的各个sheet
        self.template_df = pd.read_excel(self.base_file_path, sheet_name='外部人员模板')
        team_list_df = pd.read_excel(self.base_file_path, sheet_name='团队清单列表')
        area_price_df = pd.read_excel(self.base_file_path, sheet_name='区域价格')
        personnel_df = pd.read_excel(self.base_file_path, sheet_name='人员管理')
        business_unit_df = pd.read_excel(self.base_file_path, sheet_name='业务单元管理')
        area_mapping_df = pd.read_excel(self.base_file_path, sheet_name='分配区域')
        
        # 创建映射字典
        self.team_mapping = team_list_df.set_index(['DDLNAME', 'ENTITYID'])['DDLID'].sort_index()
        self.area_mapping = area_price_df.set_index('DDLNAME')['DDLID'].to_dict()
        self.area_name_to_id = area_mapping_df.set_index('AREANAME')['SEQID'].to_dict()
        
        if all(col in personnel_df.columns for col in ['EMPLOYEENAME', 'EMPLOYEEID', 'ENTRYID']):
            self.sales_mapping = personnel_df.set_index(['EMPLOYEENAME', 'ENTRYID'])['EMPLOYEEID'].sort_index()
            self.creator_mapping = personnel_df.set_index(['EMPLOYEENAME', 'ENTRYID'])['EMPLOYEEID'].sort_index()
        
        if all(col in business_unit_df.columns for col in ['ZX_BUSINESS_UNIT', 'UNITID', 'ENTRYID']):
            self.unit_mapping = business_unit_df.set_index(['ZX_BUSINESS_UNIT', 'ENTRYID'])['UNITID'].sort_index()
    
    def process_basic_fields(self):
        """处理基础字段"""
        # 1. 设置拼音为空，出生日期为2000-01-01
        self.template_df['拼音'] = ''
        self.template_df['出生日期 格式:yyyy-mm-dd'] = '2000-01-01'
        
        # 5. 设置失效日期
        self.template_df['失效日期（必填）格式:yyyy-mm-dd'] = '2099-01-01'
    
    def process_team_id(self):
        """处理团队ID匹配"""
        def get_team_id(ddlname, entityid=None):
            try:
                result = self.team_mapping.loc[(ddlname, entityid)]
                print(f"找到匹配团队ID: {result}")
                return result
            except (KeyError, TypeError) as e:
                return ddlname  # 回退到原值
        
        print("模板数据列名:", self.template_df.columns.tolist())
        if '独立单元ID(默认当前独立单元)' not in self.template_df.columns:
            self.template_df['独立单元ID(默认当前独立单元)'] = 1
            
        self.template_df['团队ID（必填）'] = self.template_df.apply(
            lambda row: get_team_id(row['团队ID（必填）'], row['独立单元ID(默认当前独立单元)']), axis=1)
    
    def process_area_id(self):
        """处理区域ID匹配"""
        self.template_df['区域ID（必填）'] = self.template_df['区域ID（必填）'].apply(
            lambda x: self.area_mapping.get(x, x))
    
    def process_default_fields(self):
        """处理默认字段值"""
        # 独立单元ID(默认当前独立单元)默认为1（如果不是1或5）
        if '独立单元ID(默认当前独立单元)' in self.template_df.columns:
            self.template_df['独立单元ID(默认当前独立单元)'] = self.template_df['独立单元ID(默认当前独立单元)'].apply(
                lambda x: 1 if pd.isna(x) or x not in [1, 5] else x)
        
        # 业务单元ID（必填）默认为1
        if '业务单元ID（必填）' not in self.template_df.columns:
            self.template_df['业务单元ID（必填）'] = 1
        else:
            self.template_df['业务单元ID（必填）'] = self.template_df['业务单元ID（必填）'].fillna(1)
        
        # 检查并设置所有可能缺失的字段
        required_fields = {
            '业务员ID': '',
            '制单人ID（必填）': '',
            '角色类别ID（必填）1-经销商 2-厂家 3-管理员 4-流向人员': 1,
            '行政区域ID（必填）': '',
            '业务单元ID（必填）': ''
        }
        
        for field, default in required_fields.items():
            if field not in self.template_df.columns:
                self.template_df[field] = default
            elif pd.api.types.is_numeric_dtype(self.template_df[field]):
                self.template_df[field] = self.template_df[field].fillna(default)
            else:
                self.template_df[field] = self.template_df[field].fillna('')

    def process_employee_mappings(self):
        """处理员工ID映射"""
        if self.sales_mapping is not None:
            # 处理业务员ID匹配
            def get_employee_id(name, entryid=None):
                if pd.isna(name):
                    return ''
                if str(name).isdigit():  # 如果已经是ID，直接返回
                    return str(name)
                try:
                    result = self.sales_mapping.loc[(str(name).strip().upper(), entryid)]
                    if isinstance(result, pd.Series):
                        return str(result.iloc[0])
                    return str(result)
                except (KeyError, TypeError):
                    return ''

            self.template_df['业务员ID'] = self.template_df.apply(
                lambda row: get_employee_id(row['业务员ID'], row['独立单元ID(默认当前独立单元)']), axis=1)

        if self.creator_mapping is not None:
            # 处理制单人ID匹配
            def map_creator_id(name, entryid=None):
                if pd.isna(name):
                    return ''
                if str(name).isdigit():  # 如果已经是ID，直接返回
                    return str(name)
                try:
                    result = self.creator_mapping.loc[(str(name).strip().upper(), entryid)]
                    if isinstance(result, pd.Series):
                        return str(result.iloc[0])
                    return str(result)
                except (KeyError, TypeError):
                    return ''

            self.template_df['制单人ID（必填）'] = self.template_df.apply(
                lambda row: map_creator_id(row['制单人ID（必填）'], row['独立单元ID(默认当前独立单元)']), axis=1)

    def process_role_validation(self):
        """处理角色类别ID验证"""
        if '角色类别ID（必填）1-经销商 2-厂家 3-管理员 4-流向人员' in self.template_df.columns:
            valid_roles = {1, 2, 3, 4}
            # 将无效角色设置为默认值1（经销商）
            self.template_df['角色类别ID（必填）1-经销商 2-厂家 3-管理员 4-流向人员'] = self.template_df[
                '角色类别ID（必填）1-经销商 2-厂家 3-管理员 4-流向人员'].apply(
                lambda x: x if x in valid_roles else 1)

    def process_administrative_area(self):
        """处理行政区域ID匹配"""
        def get_area_id(area_name):
            if pd.isna(area_name):
                return 61  # 如果区域名称为空，默认为61
            # 使用strip()去除可能的首尾空格，并尝试从映射字典获取ID
            return self.area_name_to_id.get(str(area_name).strip(), 61)  # 如果找不到匹配，默认为61

        self.template_df['行政区域ID（必填）'] = self.template_df['行政区域ID（必填）'].apply(get_area_id)

    def process_business_unit(self):
        """处理业务单元ID匹配"""
        if self.unit_mapping is not None:
            def get_unit_id(name, entryid=None):
                if pd.isna(name):
                    return ''
                if str(name).isdigit():  # 如果已经是ID，直接返回
                    return name
                try:
                    result = self.unit_mapping.loc[(str(name).strip(), entryid)]
                    if isinstance(result, pd.Series):
                        return result.iloc[0]
                    return result
                except (KeyError, TypeError):
                    return name  # 回退到原值

            self.template_df['业务单元ID（必填）'] = self.template_df.apply(
                lambda row: get_unit_id(row['业务单元ID（必填）'], row['独立单元ID(默认当前独立单元)']), axis=1)

    def process_all(self):
        """执行所有处理步骤"""
        print("开始处理外部人员数据...")

        # 加载数据
        self.load_data()
        print("✓ 数据加载完成")

        # 处理各个字段
        self.process_basic_fields()
        print("✓ 基础字段处理完成")

        self.process_team_id()
        print("✓ 团队ID处理完成")

        self.process_area_id()
        print("✓ 区域ID处理完成")

        self.process_default_fields()
        print("✓ 默认字段处理完成")

        self.process_employee_mappings()
        print("✓ 员工映射处理完成")

        self.process_role_validation()
        print("✓ 角色验证处理完成")

        self.process_administrative_area()
        print("✓ 行政区域处理完成")

        self.process_business_unit()
        print("✓ 业务单元处理完成")

        print("所有数据处理完成！")
        return self.template_df

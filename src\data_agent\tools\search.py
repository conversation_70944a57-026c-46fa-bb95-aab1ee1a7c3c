"""
搜索工具模块
提供Web搜索功能
"""
import os
from langchain_community.tools.tavily_search import TavilySearchResults


def create_search_tool():
    """创建Web搜索工具"""
    # Tavily 搜索引擎工具设置
    # 这个工具用于回答数据库无法解答的通用性问题
    # 它需要设置 TAVILY_API_KEY 环境变量，你可以去 https://tavily.com/ 免费注册获取
    os.environ["TAVILY_API_KEY"] = "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"  # 在这里粘贴你的Tavily Key
    
    tavily_tool = TavilySearchResults(max_results=3)
    tavily_tool.name = "web_search"  # 给工具起一个简单的名字
    tavily_tool.description = "A search engine useful for when you need to answer questions about events, data or things not in the database."
    
    return tavily_tool


if __name__ == '__main__':
    search_tool = create_search_tool()
    print("----- Search Tool Initialized -----")
    print(f"- {search_tool.name}: {search_tool.description}")

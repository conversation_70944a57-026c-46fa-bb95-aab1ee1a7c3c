"""
Excel处理工具函数
提供Excel文件读写和进程管理功能
"""
import os
import time
import psutil
import pandas as pd


def kill_excel_processes():
    """终止所有Excel进程"""
    for proc in psutil.process_iter(['name']):
        try:
            if proc.info['name'] and 'excel' in proc.info['name'].lower():
                proc.kill()
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    time.sleep(1)


def remove_file_with_retry(file_path, max_retries=3):
    """带重试机制的文件删除"""
    for attempt in range(max_retries):
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
            return True
        except Exception as e:
            print(f"Attempt {attempt + 1} failed to remove {file_path}: {str(e)}")
            if attempt < max_retries - 1:
                kill_excel_processes()
                time.sleep(2)
            else:
                print(f"Failed to remove {file_path} after {max_retries} attempts")
                return False


def safe_excel_write(df, file_path, sheet_name='Sheet1', max_retries=3):
    """安全的Excel写入，带重试机制"""
    for attempt in range(max_retries):
        try:
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name=sheet_name)
            return True
        except PermissionError:
            if attempt < max_retries - 1:
                kill_excel_processes()
                time.sleep(2)
            else:
                raise
    return False


def convert_to_xls(xlsx_file, xls_file, overwrite=False):
    """将xlsx文件转换为xls格式"""
    try:
        if os.path.exists(xls_file):
            if not overwrite:
                print(f"目标文件{xls_file}已存在，跳过转换")
                return False
            else:
                kill_excel_processes()
                remove_file_with_retry(xls_file)
        
        df = pd.read_excel(xlsx_file)
        
        try:
            import xlwt
            workbook = xlwt.Workbook()
            sheet = workbook.add_sheet('Sheet1')
            
            # 写入列标题
            for col_num, column in enumerate(df.columns):
                sheet.write(0, col_num, column)
            
            # 写入数据
            for row_num, row in df.iterrows():
                for col_num, value in enumerate(row):
                    if pd.isna(value) or str(value).startswith('#') and str(value).endswith('!'):
                        sheet.write(row_num+1, col_num, '')
                    else:
                        sheet.write(row_num+1, col_num, value)
            
            workbook.save(xls_file)
            print(f"成功保存为Excel 2003格式: {xls_file}")
            
            # 转换成功后删除原xlsx文件
            try:
                os.remove(xlsx_file)
                print(f"已删除临时文件: {xlsx_file}")
            except Exception as e:
                print(f"删除临时文件失败: {str(e)}")
            return True
            
        except ImportError:
            print("xlwt模块未安装，无法转换为.xls格式")
            return False
            
    except Exception as e:
        print(f"转换失败: {str(e)}")
        return False

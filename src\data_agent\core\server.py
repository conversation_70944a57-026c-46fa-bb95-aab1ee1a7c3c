"""
FastAPI服务器模块
提供Web API接口
"""
from fastapi import FastAPI
from fastapi.responses import RedirectResponse
from langserve import add_routes
from ..agents.graph import agent_executor

# 创建FastAPI应用
app = FastAPI(
    title="Data Agent Server",
    version="1.0",
    description="A server for the Data Agent powered by LangGraph",
)

# 默认根路径跳转到 /docs
@app.get("/")
async def redirect_root_to_docs():
    return RedirectResponse("/docs")


# 使用 add_routes 将我们的 agent_executor 添加到 FastAPI 应用中
# 这会自动创建多个API端点，例如 /invoke, /batch, /stream 等
# 我们主要会使用 /stream 来实现打字机效果
add_routes(
    app,
    agent_executor,
    path="/data-agent",
    # 开启 playground，方便在浏览器中直接测试API
    playground_type="default",
)


def start_server():
    """启动服务器"""
    import uvicorn
    # 启动服务
    # host="0.0.0.0" 让局域网内的其他设备也能访问
    # port=8000 是标准的API服务端口
    uvicorn.run(app, host="0.0.0.0", port=8000)


if __name__ == "__main__":
    start_server()

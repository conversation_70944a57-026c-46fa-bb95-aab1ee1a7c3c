# Data Agent - 数据处理智能体

一个集成了LangChain和数据处理功能的智能体项目，主要用于外部人员管理数据处理和智能查询。

## ✨ 主要功能

- 🤖 **智能数据查询**: 基于LangChain的SQL数据库查询智能体
- 📊 **Excel数据处理**: 外部人员管理数据的自动化处理和转换
- 🔍 **Web搜索集成**: 集成Tavily搜索引擎，支持通用信息查询
- 🌐 **Web API服务**: 基于FastAPI的RESTful API接口
- 💬 **交互式命令行**: 支持命令行交互模式

## 🏗️ 项目结构

```
data_agent/
├── src/                    # 源代码目录
│   └── data_agent/         # 主包目录
│       ├── __init__.py
│       ├── core/           # 核心业务逻辑
│       │   ├── personnel_processor.py  # 人员数据处理器
│       │   └── server.py               # Web服务器
│       ├── agents/         # AI智能体相关
│       │   └── graph.py                # LangGraph智能体
│       ├── tools/          # 工具模块
│       │   ├── database.py             # 数据库工具
│       │   ├── search.py               # 搜索工具
│       │   └── all_tools.py            # 工具集合
│       └── utils/          # 工具函数
│           └── excel_utils.py          # Excel处理工具
├── scripts/                # 独立脚本
│   └── generate_external_personnel.py # 外部人员数据生成脚本
├── data/                   # 数据文件
│   ├── raw/               # 原始数据
│   ├── processed/         # 处理后数据
│   └── templates/         # 模板文件
├── tests/                  # 测试文件
├── docs/                   # 文档
├── config/                 # 配置文件
│   └── .env.example       # 环境变量示例
├── requirements/           # 依赖管理
│   ├── base.txt           # 基础依赖
│   └── dev.txt            # 开发依赖
├── main.py                 # 主入口文件
├── pyproject.toml          # 项目配置
└── README.md              # 项目说明
```

## 🚀 快速开始

### 1. 环境准备

确保您的系统已安装Python 3.11或更高版本。

### 2. 安装依赖

使用uv（推荐）：
```bash
uv sync
```

或使用pip：
```bash
pip install -r requirements/base.txt
```

### 3. 配置环境变量

复制配置文件模板：
```bash
cp config/.env.example .env
```

编辑`.env`文件，填入您的API密钥和数据库配置：
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=your_database

# DeepSeek API配置
DEEPSEEK_API_KEY=your_deepseek_api_key

# Tavily搜索API配置
TAVILY_API_KEY=your_tavily_api_key
```

### 4. 准备数据

将您的`底表.xlsx`文件放置在`data/raw/`目录下。

## 📖 使用方法

### 命令行模式

#### 交互式模式（默认）
```bash
python main.py
# 或
python main.py interactive
```

#### 启动Web服务器
```bash
python main.py server
```
服务器将在 http://localhost:8000 启动，您可以访问 http://localhost:8000/docs 查看API文档。

#### 运行数据处理脚本
```bash
python main.py personnel
# 或直接运行脚本
python scripts/generate_external_personnel.py
```

### 脚本参数

外部人员数据处理脚本支持以下参数：
```bash
python scripts/generate_external_personnel.py \
    --input data/raw/底表.xlsx \
    --output-dir data/processed \
    --format both
```

参数说明：
- `--input`: 输入的底表Excel文件路径
- `--output-dir`: 输出目录
- `--format`: 输出格式（xlsx/xls/both）

## 🔧 开发指南

### 安装开发依赖
```bash
pip install -r requirements/dev.txt
```

### 运行测试
```bash
pytest
```

### 代码格式化
```bash
black src/ scripts/ tests/
isort src/ scripts/ tests/
```

### 类型检查
```bash
mypy src/
```

## 📚 API文档

启动Web服务器后，访问以下地址查看API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🤝 贡献指南

1. Fork 本仓库
2. 创建您的特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开一个 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如果您遇到任何问题或有任何建议，请：
1. 查看 [文档](docs/)
2. 搜索 [已有问题](https://github.com/your-org/data-agent/issues)
3. 创建 [新问题](https://github.com/your-org/data-agent/issues/new)

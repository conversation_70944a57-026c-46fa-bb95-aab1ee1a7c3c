# 数据目录说明

这个目录包含项目使用的所有数据文件。

## 目录结构

```
data/
├── raw/                    # 原始数据文件
│   └── 底表.xlsx          # 外部人员管理基础数据表
├── processed/              # 处理后的数据文件
│   ├── 外部人员管理.xlsx   # 处理后的Excel文件
│   └── 外部人员管理.xls    # 处理后的XLS文件
└── templates/              # 数据模板文件
```

## 文件说明

### raw/底表.xlsx
这是外部人员管理系统的基础数据表，包含以下工作表：
- **外部人员模板**: 外部人员信息模板
- **团队清单列表**: 团队信息和ID映射
- **区域价格**: 区域信息和价格数据
- **人员管理**: 员工信息和ID映射
- **业务单元管理**: 业务单元信息
- **分配区域**: 行政区域映射

### processed/
存放经过脚本处理后的输出文件：
- `外部人员管理.xlsx`: 标准Excel格式的处理结果
- `外部人员管理.xls`: Excel 2003格式的处理结果

## 使用说明

1. 将原始数据文件放在 `raw/` 目录下
2. 运行处理脚本：`python scripts/generate_external_personnel.py`
3. 处理结果将保存在 `processed/` 目录下

## 注意事项

- 请确保原始数据文件格式正确
- 处理过程中会自动创建必要的目录
- 如果输出文件已存在，脚本会询问是否覆盖

# ===========================================
# Data Agent 环境变量配置模板
# ===========================================
#
# 使用说明：
# 1. 复制此文件到项目根目录
# 2. 重命名为 .env
# 3. 填入真实的配置值
# 4. 不要将 .env 文件提交到版本控制
#
# ===========================================

# 数据库配置
# MySQL数据库连接信息
DB_HOST=localhost                    # 数据库主机地址
DB_PORT=3306                        # 数据库端口
DB_USER=your_username               # 数据库用户名
DB_PASSWORD=your_password           # 数据库密码
DB_NAME=your_database               # 数据库名称

# DeepSeek API配置
# 获取地址: https://platform.deepseek.com/
DEEPSEEK_API_KEY=your_deepseek_api_key

# Tavily搜索API配置（用于Web搜索功能）
# 获取地址: https://tavily.com/
TAVILY_API_KEY=your_tavily_api_key

# LangSmith配置（可选，用于调试和监控）
# 获取地址: https://smith.langchain.com/
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your_langsmith_api_key
LANGCHAIN_PROJECT=Data Agent Project

# 服务器配置
SERVER_HOST=0.0.0.0                 # 服务器监听地址
SERVER_PORT=8000                    # 服务器端口

# 日志配置
LOG_LEVEL=INFO                      # 日志级别: DEBUG, INFO, WARNING, ERROR
LOG_FILE=logs/data_agent.log        # 日志文件路径
